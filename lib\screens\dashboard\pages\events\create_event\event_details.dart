import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:mime/mime.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/main.dart' show isLight;

class EventDetails extends StatelessWidget {
  final TextEditingController eventTitle, emailAddress, phoneNumber;
  final q.QuillController eventDescription;
  // final TextEditingController eventDescription;
  final GlobalKey<FormState> formKey;

  EventDetails(
      {super.key,
      required this.eventTitle,
      required this.eventDescription,
      required this.emailAddress,
      required this.phoneNumber,
      required this.formKey});
  final _controller = Get.find<CreateEventController>();

  @override
  Widget build(BuildContext context) {
    _controller.bannerList.clear();
    final controller = Get.find<CreateEventController>();
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Event title is required';
                  }
                  if (_controller.eventMedia.isEmpty) {
                    return 'Event banner is required';
                  }
                  return null;
                },
                controller: eventTitle,
                title: 'Event Title',
                hint: 'e.g Kenya awards night'),
            SizedBox(height: 16.h),
            Text(
              'Event Description',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.grey.shade400),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    q.QuillSimpleToolbar(
                      controller: eventDescription,
                      config: const q.QuillSimpleToolbarConfig(
                        multiRowsDisplay: false,
                      ),
                    ),
                    const SizedBox(height: 15),
                    q.QuillEditor.basic(
                      controller: eventDescription,
                      config: const q.QuillEditorConfig(
                        placeholder:
                            "Describe your event",

                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email address is required';
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'Enter a valid email address';
                  }

                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                controller: emailAddress,
                title: 'Email Address',
                hint: 'e.g <EMAIL>'),
            SizedBox(height: 16.h),
            CustomInternationalPhoneInput(
              onInputChanged: (
                PhoneNumber number,
              ) {
                phoneNumber.text =
                    number.phoneNumber.toString().replaceAll("+", '');
                print(phoneNumber.text);
              },
              onInputValidated: (bool value) {},
              ignoreBlank: false,
              initialValue: PhoneNumber(
                  isoCode: 'KE',
                  dialCode: '+254',
                  phoneNumber: "+${phoneNumber.text}"),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Phone Number is required';
                }
                return null;
              },
              formatInput: true,
            ),
            SizedBox(height: 16.h),
            Text(
              'Category',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            GetX<CreateEventController>(
                builder: (controller) {
                  // Ensure categories are loaded
                  if (controller.categories.isEmpty && !controller.isLoadingCategories.value) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      controller.getCategories();
                    });
                  }
                  if (controller.isLoadingCategories.isTrue) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Select Category'),
                          CupertinoActivityIndicator()
                        ],
                      ),
                    );
                  } else if (controller.categories.isEmpty) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: const Center(child: Text('No categories found')),
                    );
                  } else {
                    return DropdownButtonFormField<CategoriesModel>(
                      value: controller.selCategory?.value,
                      decoration: InputDecoration(
                        hintText: "Select Category",
                        filled: true,
                        fillColor:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: const BorderSide(
                            width: 0.5,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      items: controller.categories.map((category) {
                        return DropdownMenuItem<CategoriesModel>(
                          value: category,
                          child: Text(category.title ?? ''),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.category.value = value.id!;
                          controller.selCategory?.value = value;
                        }
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Category is required';
                        }
                        return null;
                      },
                    );
                  }
                }),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Banner',
                  style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                ),
                Obx(() => Text(
                  _controller.getMediaLimitMessage(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: _controller.isMediaLimitReached ? Colors.orange : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                )),
              ],
            ),
            SizedBox(height: 8.h),
            GestureDetector(
              onTap: () async {
                // Check media limit first
                if (_controller.isMediaLimitReached) {
                  showSnackbar(
                      context: context,
                      label: 'Maximum ${CreateEventController.maxMediaLimit} media items allowed',
                      color: Colors.orange);
                  return;
                }

                final hasUploading = _controller.bannerList.any((item) =>
                    item['uploading'] == true);
                if (!hasUploading) {
                  final result = await FilePicker.platform.pickFiles(
                    allowMultiple: false,
                    type: FileType.image,
                    allowedExtensions: null,
                  );
                  if (result == null || result.files.isEmpty) {
                    ToastUtils.showToast('Nothing picked');
                    return;
                  }
                  
                  final file = result.files.first;
                  
                  // Validate file size (max 10MB)
                  if (file.size > 10 * 1024 * 1024) {
                    ToastUtils.showToast('File size must be less than 10MB');
                    return;
                  }
                  
                  final String mimeType = lookupMimeType(file.path ?? '') ?? '';
                  if (!mimeType.startsWith('image/')) {
                    ToastUtils.showToast('Please select an image file');
                    return;
                  }

                  if (result.files.isNotEmpty) {
                    final filePath = result.xFiles.first.path;
                    final tempId = DateTime.now().millisecondsSinceEpoch.toString();
                    
                    // Create combined item with unique ID for tracking
                    final bannerItem = {
                      "id": tempId,
                      "name": filePath,
                      "uploading": true,
                      "url": null
                    };
                    
                    _controller.bannerList.add(bannerItem);
                    
                    try {
                      final url = await _controller.uploadFile(
                          path: filePath,
                          fileName:
                              "${DateTime.now().millisecondsSinceEpoch}_${filePath.split(RegExp(r'[/\\]')).last}");
                      
                      if (url != null && url.isNotEmpty) {
                        // Update the banner item with URL and mark as completed
                        final index = _controller.bannerList.indexWhere((item) => item['id'] == tempId);
                        if (index != -1) {
                          _controller.bannerList[index] = {
                            "id": tempId,
                            "name": filePath,
                            "uploading": false,
                            "url": url
                          };
                          _controller.eventMedia.add({'id': tempId, 'url': url, 'type': "image"});
                          ToastUtils.showToast('Image uploaded successfully');
                        }
                      } else {
                        // Remove failed upload
                        _controller.bannerList.removeWhere((item) =>
                            item['id'] == tempId);
                        showSnackbar(
                            context: context, 
                            label: 'Upload failed. Please try again.',
                            color: Colors.red);
                      }
                    } catch (e) {
                      // Remove failed upload
                      _controller.bannerList.removeWhere((item) =>
                          item['id'] == tempId);
                      showSnackbar(
                          context: context, 
                          label: 'Upload failed: ${e.toString()}',
                          color: Colors.red);
                    }
                  }
                } else {
                  showSnackbar(
                      context: context,
                      label: 'Please wait for the current upload to complete',
                      color: Colors.orange);
                }
              },
              child: Obx(() => Container(
                height: 55.h,
                padding: const EdgeInsets.all(8),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: _controller.isMediaLimitReached
                      ? Colors.grey.withOpacity(0.3)
                      : (isLight.value ? Colors.white70 : Colors.transparent),
                  border: Border.all(
                    color: _controller.isMediaLimitReached ? Colors.grey.withOpacity(0.5) : Colors.grey
                  ),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Obx(() => Text(
                      _controller.isMediaLimitReached
                          ? 'Media Limit Reached (${CreateEventController.maxMediaLimit}/${CreateEventController.maxMediaLimit})'
                          : 'Upload Event Banner',
                      style: TextStyle(
                        fontSize: 14.spMin,
                        color: _controller.isMediaLimitReached ? Colors.grey : null,
                      ),
                    )),
                    SizedBox(width: 8.w),
                    Obx(() {
                      final hasUploading = _controller.bannerList.any((item) => 
                          item['uploading'] == true);
                      return hasUploading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.upload_file);
                    }),
                  ],
                ),
              )),
            ),
            SizedBox(
              height: 100,
              child: Obx(() {
                return ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.bannerList.length,
                    itemBuilder: (context, index) {
                      final bannerItem = controller.bannerList[index];
                      final isUploading = bannerItem['uploading'] == true;
                      
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Image.file(
                              File("${bannerItem['name']}"),
                              height: 80,
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                            if (isUploading)
                              Container(
                                color: Colors.black54,
                                child: const Center(
                                  child: CircularProgressIndicator(color: Colors.white),
                                ),
                              )
                            else
                              Positioned(
                                top: 0,
                                right: 0,
                                child: IconButton(
                                  icon: const Icon(Icons.close, color: Colors.red),
                                  onPressed: () {
                                    final itemId = bannerItem['id'];
                                    // Remove from both lists using ID matching
                                    controller.bannerList.removeWhere((item) =>
                                        item['id'] == itemId);
                                    controller.eventMedia.removeWhere((media) =>
                                        media['id'] == itemId);
                                  },
                                ),
                              )
                          ],
                        ),
                      );
                    });
              }),
            ),
            SizedBox(height: 200.h)
          ],
        ),
      ),
    );
  }
}
