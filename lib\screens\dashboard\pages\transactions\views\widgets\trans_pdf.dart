import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
/// Generate and save PDF with modern design
Future<void> generateAndSavePDF(TransactionModel transaction) async {
  try {
    final pdf = pw.Document();
    final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
    final DateTime createdAt = transaction.createdAt ?? DateTime.now();

    // Safely get DataController with null check
    DataController? dataController;
    try {
      dataController = Get.find<DataController>();
    } catch (e) {
      // DataController not found, continue without it
      dataController = null;
    }

    // Load logo with error handling
    Uint8List logoBytes;
    try {
      final ByteData logoData = await rootBundle.load('assets/images/launcher.png');
      logoBytes = logoData.buffer.asUint8List();
    } catch (e) {
      // Create a simple placeholder if logo loading fails
      logoBytes = Uint8List(0);
    }

  pdf.addPage(
    pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(40),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            // Header Section with Logo and Company Info
            _buildPDFHeader(logoBytes),
            
            pw.SizedBox(height: 40),
            
            // Receipt Content Card - Simplified without boxShadow
            pw.Container(
              width: double.infinity,
              decoration: pw.BoxDecoration(
                color: PdfColors.white,
                border: pw.Border.all(color: PdfColors.grey300, width: 2),
                borderRadius: pw.BorderRadius.circular(12),
              ),
              child: pw.Column(
                children: [
                  // Receipt Header
                  _buildReceiptHeader(transaction, format, createdAt),

                  // Customer Information Section - Always show if any data exists
                  _buildCustomerSection(transaction),

                  // Transaction Details Section
                  _buildTransactionSection(transaction, dataController),

                  // Amount Section (Highlighted)
                  _buildAmountSection(transaction),

                  // Footer inside card
                  _buildReceiptFooter(transaction),
                ],
              ),
            ),
            
            pw.SizedBox(height: 30),
            
            // Company Footer
            _buildCompanyFooter(),
          ],
        );
      },
    ),
  );

    // Save and share PDF
    await _savePDF(pdf, transaction);
  } catch (e) {
    // Handle any errors during PDF generation
    print('Error generating PDF: $e');
    // In a real app, you might want to show an error message to the user
    // For now, we'll just print the error to avoid dependency issues
    rethrow; // Re-throw so calling code can handle it
  }
}

/// Build PDF Header with logo and company info
pw.Widget _buildPDFHeader(Uint8List logoBytes) {
  return pw.Column(
    children: [
      // Logo with fallback
      pw.Container(
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey50,
          borderRadius: pw.BorderRadius.circular(12),
        ),
        child: logoBytes.isNotEmpty
          ? pw.Image(
              pw.MemoryImage(logoBytes),
              height: 60,
              width: 120,
            )
          : pw.Container(
              height: 60,
              width: 120,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey200,
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Center(
                child: pw.Text(
                  'LOGO',
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey600,
                  ),
                ),
              ),
            ),
      ),
      
      pw.SizedBox(height: 16),
      
      // Company Name
      pw.Text(
        'OneKitty',
        style: pw.TextStyle(
          fontSize: 32,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.grey800,
        ),
      ),
      
      pw.SizedBox(height: 8),
      
      // Transaction Receipt Label
      pw.Container(
        padding: const pw.EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: pw.BoxDecoration(
          color: PdfColors.green100,
          borderRadius: pw.BorderRadius.circular(20),
        ),
        child: pw.Text(
          'TRANSACTION RECEIPT',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.green800,
            letterSpacing: 1.2,
          ),
        ),
      ),
    ],
  );
}

/// Build Receipt Header Section
pw.Widget _buildReceiptHeader(TransactionModel transaction,DateFormat format, DateTime createdAt) {
  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(24),
    decoration: const pw.BoxDecoration(
      color: PdfColors.green50,
      borderRadius: pw.BorderRadius.only(
        topLeft: pw.Radius.circular(12),
        topRight: pw.Radius.circular(12),
      ),
    ),
    child: pw.Column(
      children: [
        pw.Text(
          'Receipt Details',
          style: pw.TextStyle(
            fontSize: 20,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.green800,
          ),
        ),
        
        pw.SizedBox(height: 16),
        
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            _buildInfoItem(
              'Receipt No.',
              transaction.transactionCode ?? 'N/A',
              PdfColors.green700,
            ),
            _buildInfoItem(
              'Date & Time',
              format.format(createdAt),
              PdfColors.green700,
            ),
          ],
        ),
        
        pw.SizedBox(height: 12),
        
        _buildStatusBadge(transaction.status ?? 'COMPLETED'),
      ],
    ),
  );
}

/// Build Customer Information Section
pw.Widget _buildCustomerSection(TransactionModel transaction) {
  // Check if we have any customer data to display
  final hasCustomerName = (transaction.firstName?.isNotEmpty == true) ||
                         (transaction.secondName?.isNotEmpty == true);
  final hasPhoneNumber = transaction.phoneNumber?.isNotEmpty == true;

  // Only show section if we have customer data
  if (!hasCustomerName && !hasPhoneNumber) {
    return pw.SizedBox.shrink();
  }

  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(24),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section header without icon
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Text(
            'Customer Information',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
        ),

        pw.SizedBox(height: 16),

        // Customer name if available
        if (hasCustomerName)
          _buildDetailRow(
            'Customer Name',
            '${transaction.firstName ?? ''} ${transaction.secondName ?? ''}'.trim(),
          ),

        // Phone number if available
        if (hasPhoneNumber)
          _buildDetailRow(
            'Phone Number',
            transaction.phoneNumber!,
          ),

        // Divider
        pw.Container(
          margin: const pw.EdgeInsets.only(top: 16),
          height: 1,
          color: PdfColors.grey200,
        ),
      ],
    ),
  );
}

/// Build Transaction Details Section
pw.Widget _buildTransactionSection(TransactionModel transaction, DataController? dataController) {
  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(24),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section header without icon
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: pw.BoxDecoration(
            color: PdfColors.orange50,
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Text(
            'Transaction Details',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.orange800,
            ),
          ),
        ),

        pw.SizedBox(height: 16),

        // Kitty group with null safety
        if (dataController?.kitty.value.kitty?.title?.isNotEmpty == true)
          _buildDetailRow(
            'Kitty Group',
            dataController!.kitty.value.kitty!.title!,
          )
        else if (transaction.kittyTitle?.isNotEmpty == true)
          _buildDetailRow(
            'Kitty Group',
            transaction.kittyTitle!,
          ),

        // Transaction type with null safety
        if (transaction.product?.isNotEmpty == true)
          _buildDetailRow(
            'Transaction Type',
            transaction.product!.toUpperCase(),
          ),

        // Payment method - always show
        _buildDetailRow(
          'Payment Method',
          transaction.channelName ?? 'Mobile Money',
        ),

        // Transaction status if available
        if (transaction.status?.isNotEmpty == true)
          _buildDetailRow(
            'Status',
            transaction.status!.toUpperCase(),
          ),

        // Divider
        pw.Container(
          margin: const pw.EdgeInsets.only(top: 16),
          height: 1,
          color: PdfColors.grey200,
        ),
      ],
    ),
  );
}


/// Build Amount Section (Highlighted)
pw.Widget _buildAmountSection(TransactionModel transaction) {
  // Safely get the amount with null check
  final amount = transaction.amount ?? 0;
  final formattedAmount = _safeFormatCurrency(amount);

  return pw.Container(
    width: double.infinity,
    margin: const pw.EdgeInsets.all(24),
    padding: const pw.EdgeInsets.all(24),
    decoration: pw.BoxDecoration(
      color: PdfColors.green50,
      borderRadius: pw.BorderRadius.circular(12),
      border: pw.Border.all(color: PdfColors.green200, width: 2),
    ),
    child: pw.Column(
      children: [
        pw.Text(
          'TOTAL AMOUNT',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.green700,
            letterSpacing: 1.1,
          ),
        ),

        pw.SizedBox(height: 8),

        pw.Text(
          'KES $formattedAmount',
          style: pw.TextStyle(
            fontSize: 32,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.green800,
          ),
        ),
      ],
    ),
  );
}

/// Safely format currency with null check
String _safeFormatCurrency(dynamic amount) {
  try {
    if (amount == null) return '0.00';
    return FormattedCurrency.getFormattedCurrency(amount);
  } catch (e) {
    // Fallback formatting if FormattedCurrency fails
    final numAmount = double.tryParse(amount.toString()) ?? 0.0;
    return numAmount.toStringAsFixed(2);
  }
}

/// Build Receipt Footer
pw.Widget _buildReceiptFooter(TransactionModel transaction) {
  // Safely get transaction code
  final transactionCode = transaction.transactionCode?.isNotEmpty == true
      ? transaction.transactionCode!
      : 'ONEKITTY${DateTime.now().millisecondsSinceEpoch}';

  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(24),
    decoration: const pw.BoxDecoration(
      color: PdfColors.grey50,
      borderRadius: pw.BorderRadius.only(
        bottomLeft: pw.Radius.circular(12),
        bottomRight: pw.Radius.circular(12),
      ),
    ),
    child: pw.Column(
      children: [
        // Barcode with error handling
        pw.Container(
          height: 60,
          child: _buildSafeBarcode(transactionCode),
        ),

        pw.SizedBox(height: 16),

        pw.Text(
          'Transaction Code: $transactionCode',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey700,
          ),
        ),

        pw.SizedBox(height: 8),

        pw.Text(
          'Keep this receipt for your records',
          style: pw.TextStyle(
            fontSize: 10,
            color: PdfColors.grey600,
            fontStyle: pw.FontStyle.italic,
          ),
        ),
      ],
    ),
  );
}

/// Build barcode with error handling
pw.Widget _buildSafeBarcode(String data) {
  try {
    return pw.BarcodeWidget(
      barcode: pw.Barcode.code128(),
      data: data,
      width: 200,
      height: 60,
      drawText: false,
    );
  } catch (e) {
    // Fallback if barcode generation fails
    return pw.Container(
      width: 200,
      height: 60,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Center(
        child: pw.Text(
          data,
          style: pw.TextStyle(
            fontSize: 10,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey700,
          ),
        ),
      ),
    );
  }
}

/// Build Company Footer
pw.Widget _buildCompanyFooter() {
  return pw.Column(
    children: [
      pw.Text(
        'Thank you for choosing OneKitty!',
        style: pw.TextStyle(
          fontSize: 18,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.grey700,
        ),
      ),
      
      pw.SizedBox(height: 8),
      
      pw.Text(
        'Your trusted financial companion',
        style: pw.TextStyle(
          fontSize: 12,
          color: PdfColors.grey500,
          fontStyle: pw.FontStyle.italic,
        ),
      ),
      
      pw.SizedBox(height: 12),
      
      pw.Container(
        padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(20),
        ),
        child: pw.Text(
          'www.onekitty.co.ke',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey700,
          ),
        ),
      ),
    ],
  );
}

/// Build info item for header
pw.Widget _buildInfoItem(String label, String value, PdfColor color) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.center,
    children: [
      pw.Text(
        label,
        style: pw.TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.SizedBox(height: 4),
      pw.Text(
        value,
        style: pw.TextStyle(
          fontSize: 12,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.grey800,
        ),
      ),
    ],
  );
}

/// Build status badge
pw.Widget _buildStatusBadge(String status) {
  final isSuccess = status.toUpperCase() == 'COMPLETED' || status.toUpperCase() == 'SUCCESS';
  
  return pw.Container(
    padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 6),
    decoration: pw.BoxDecoration(
      color: isSuccess ? PdfColors.green200 : PdfColors.orange200,
      borderRadius: pw.BorderRadius.circular(20),
    ),
    child: pw.Text(
      status.toUpperCase(),
      style: pw.TextStyle(
        fontSize: 10,
        fontWeight: pw.FontWeight.bold,
        color: isSuccess ? PdfColors.green800 : PdfColors.orange800,
        letterSpacing: 0.5,
      ),
    ),
  );
}

/// Build detail row
pw.Widget _buildDetailRow(String label, String value) {
  return pw.Padding(
    padding: const pw.EdgeInsets.symmetric(vertical: 6),
    child: pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.SizedBox(
          width: 140,
          child: pw.Text(
            '$label:',
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.Expanded(
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
        ),
      ],
    ),
  );
}

/// Save PDF file
Future<void> _savePDF(pw.Document pdf,TransactionModel transaction) async {
  final output = await getApplicationDocumentsDirectory();
  final fileName = 'receipt_${transaction.transactionCode ?? DateTime.now().millisecondsSinceEpoch}.pdf';
  final file = File('${output.path}/$fileName');
  
  await file.writeAsBytes(await pdf.save());

  // Share the PDF file
  await Printing.sharePdf(
    bytes: await pdf.save(),
    filename: fileName,
  );
}