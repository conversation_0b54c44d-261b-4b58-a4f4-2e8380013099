import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:dio/dio.dart' as dios;
import '/models/events/media_models.dart';
import 'events_controller.dart';
import 'view_single_event.dart';

class EditEventController extends GetxController implements GetxService {
  Rx<Event> event = Rx<Event>(Event(
    id: 0,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    deletedAt: null,
    title: '',
    username: '',
    description: '',
    email: '',
    venue: '',
    latitude: 0.0,
    longitude: 0.0,
    locationTip: '',
    categoryId: 0,
    category: CategoriesModel(
        id: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        deletedAt: null,
        title: "",
        description: "",
        avatar: ""),
    tickets: [],
    eventMedia: [],
    socialAccounts: [],
    startDate: DateTime.now(),
    endDate: DateTime.now(),
  ));
  RxBool isloading = false.obs;
  RxBool isFetchingMedia = false.obs;
  var logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  RxDouble long = 0.0.obs, lat = 0.0.obs;
  RxList<CategoriesModel> categories = <CategoriesModel>[].obs;
  RxBool isLoadingCategories = false.obs;
  RxBool isUploading = false.obs;
  RxBool isEditing = false.obs;
  RxBool hasSignatoryTransactions = false.obs;

  RxInt category = 0.obs;
  RxList<EventMedia> eventMedia = <EventMedia>[].obs;
  final Rx<CategoriesModel?> eventcategory = Rx<CategoriesModel?>(null);

  // Media limit constant
  static const int maxMediaLimit = 3;

  // Media trimming state tracking
  RxBool hasMediaBeenTrimmed = false.obs;
  RxInt originalMediaCount = 0.obs;
  RxString mediaTrimWarningMessage = ''.obs;

  // Getter to check if media limit is reached
  bool get isMediaLimitReached => eventMedia.length >= maxMediaLimit;

  // Getter to get remaining media slots
  int get remainingMediaSlots => maxMediaLimit - eventMedia.length;

  // Method to get media limit message
  String getMediaLimitMessage() {
    if (isMediaLimitReached) {
      return 'Maximum $maxMediaLimit media items allowed';
    }
    return '${eventMedia.length}/$maxMediaLimit media items added';
  }

  // Method to get media trim warning message
  String getMediaTrimWarningMessage() {
    if (hasMediaBeenTrimmed.value) {
      return 'Warning: This event had ${originalMediaCount.value} media items. Only the first $maxMediaLimit have been kept. Further modifications will permanently remove the excess media.';
    }
    return '';
  }

  @override
  void onInit() {
    super.onInit();
    initializeController();
  }

  @override
  void onClose() {
    clearVariables();
    super.onClose();
  }

  void initializeController() {
    // Load categories on initialization
    getCategories();
  }

  void clearVariables() {
    // Clear all reactive variables
    isloading.value = false;
    isFetchingMedia.value = false;
    isLoadingCategories.value = false;
    isUploading.value = false;
    isEditing.value = false;
    hasSignatoryTransactions.value = false;
    long.value = 0.0;
    lat.value = 0.0;
    category.value = 0;
    categories.clear();
    eventMedia.clear();
    eventcategory.value = null;

    // Clear media trimming state
    hasMediaBeenTrimmed.value = false;
    originalMediaCount.value = 0;
    mediaTrimWarningMessage.value = '';
  }

  void loadEventData(MyEventsModel myEvent) {
    event.value = myEvent.event;
    lat.value = event.value.latitude;
    long.value = event.value.longitude;
    category.value = event.value.categoryId ?? 0;
    eventcategory.value = event.value.category;

    // Enforce media limit when loading event data
    _enforceMediaLimitOnLoad(myEvent.event.eventMedia ?? []);
  }

  /// Enforces the 3-media limit when loading existing event data
  /// Shows warning if media was trimmed
  void _enforceMediaLimitOnLoad(List<EventMedia> originalMedia) {
    originalMediaCount.value = originalMedia.length;

    if (originalMedia.length > maxMediaLimit) {
      // Trim to first 3 media items
      eventMedia.assignAll(originalMedia.take(maxMediaLimit).toList());

      // Set warning state
      hasMediaBeenTrimmed.value = true;
      mediaTrimWarningMessage.value = getMediaTrimWarningMessage();

      logger.w('Event media trimmed from ${originalMedia.length} to $maxMediaLimit items');

      // Log the warning for debugging
      logger.i('Media limit enforced: keeping first $maxMediaLimit out of ${originalMedia.length} media items');
    } else {
      // No trimming needed
      eventMedia.assignAll(originalMedia);
      hasMediaBeenTrimmed.value = false;
      originalMediaCount.value = originalMedia.length;
      mediaTrimWarningMessage.value = '';
    }
  }

  /// Shows media limit warning to user if media was trimmed
  void showMediaLimitWarningIfNeeded(BuildContext context) {
    if (hasMediaBeenTrimmed.value) {
      // Use a delayed call to ensure the UI is ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (context.mounted) {
          showSnackbar(
            context: context,
            label: 'Media Limit Notice: This event had ${originalMediaCount.value} media items. Only the first $maxMediaLimit are shown. Further edits will permanently remove excess media.',
            color: Colors.orange,
            duration: 6,
          );
        }
      });
    }
  }

  Future<void> getCategories() async {
    try {
      isLoadingCategories(true);
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETCATEGORIES);

      if (response.data != null) {
        final returneddata = response.data['data']['categories'] as List;
        categories = returneddata
            .map((item) {
              return CategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
      } else {
        logger.v('No data or unexpected response structure');
      }
      Future.microtask(() => isLoadingCategories(false));
    } catch (e) {
      logger.e('Error fetching events: $e');
    } finally {
      isLoadingCategories(false);
    }
  }

  Future<bool> fetchEventDetailbyUsername(String username,
      {bool? isOrganizer}) async {
    try {
      isloading(true);

      final response = await apiProvider.request(
          method: Method.GET, url: "${ApiUrls.GETEVENTBYUSERNAME}$username");
      if (response.data != null) {
        final returneddata = response.data['data'] as Map<String, dynamic>;
        final myEvent = MyEventsModel.fromJson(returneddata);
        event.value = myEvent.event;
        Get.put(ViewSingleEventController()).event.value = event.value;
        lat = event.value.latitude.obs;
        long = event.value.longitude.obs;

        // Enforce media limit when loading event data
        _enforceMediaLimitOnLoad(event.value.eventMedia ?? []);

        eventcategory(event.value.category);
        update();

        isloading(false);
        return true;
      } else {
        logger.v('No data or unexpected response structure');
      }

      isloading(false);
      return false;
    } catch (e) {
      logger.e('Error fetching events: $e');
      Future.delayed(Duration.zero, () {
        isloading(false);
      });
      return false;
    }
  }

  Future<void> fetchEventDetail(int id, {bool? isOrganizer}) async {
    try {
      isloading(true);

      final response = await apiProvider.request(
          method: Method.GET, url: "${ApiUrls.GETEVENTBYID}$id");
      if (response.data != null) {
        final returneddata = response.data['data'] as Map<String, dynamic>;
        final myEvent = MyEventsModel.fromJson(returneddata);
        event.value = myEvent.event;
        lat = event.value.latitude.obs;
        long = event.value.longitude.obs;

        // Enforce media limit when loading event data
        _enforceMediaLimitOnLoad(event.value.eventMedia ?? []);

        eventcategory(event.value.category);
        if (response.data['data']['has_signatory_transactions'] != null) {
          hasSignatoryTransactions(
              response.data['data']['has_signatory_transactions']);
        }
// THIS SHOULD BE ABLE TO UPDATE THE REFRESHED EVENT TO THE MAIN LIST
        final eventsController = Get.find<Eventcontroller>();
        Get.find<ViewSingleEventController>().event.value = event.value;
        if (isOrganizer ?? false) {
          int index =
              eventsController.userEvents.indexWhere((e) => e.event.id == id);
          if (index != -1) {
            Get.find<Eventcontroller>().userEvents[index] = myEvent;
          }
          update();
        }
      } else {
        logger.v('No data or unexpected response structure');
      }
      isloading(false);
    } catch (e) {
      logger.e('Error fetching events: $e');
      Future.delayed(Duration.zero, () {
        isloading(false);
      });
    }
  }

  Future<Map<String, dynamic>?> uploadFile(String path) async {
    try {
      var data = dios.FormData.fromMap({
        'file': await dios.MultipartFile.fromFile(path,
            filename:
                "${DateTime.now().millisecondsSinceEpoch}_${path.split(RegExp(r'[/\\]')).last}"),
        'bucket': 'onekitty'
      });

      final response = await apiProvider.request(
        url: ApiUrls.UPLOADFILE,
        method: Method.POST,
        formdata: data,
      );

      if (response.data != null && response.data['status'] != null) {
        if (response.data['status'] == true) {
          logger.i('File uploaded successfully: ${response.data['message']}');
          return response.data['data'];
        } else {
          final errorMsg = response.data['message'] ?? 'Upload failed';
          logger.e('File upload failed: $errorMsg');
          return null;
        }
      } else {
        logger.e('Invalid response format from server');
        return null;
      }
    } catch (e) {
      logger.e('Error uploading file: $e');
      return null;
    }
  }

  Future<bool> updateEventMedia(int id,
      {String? addMediaUrl, int? removeAtIndex}) async {
    try {
      // Handle addition
      if (addMediaUrl != null) {
        // Check media limit before adding
        if (eventMedia.length >= maxMediaLimit) {
          logger.w(
              'Cannot add more media. Maximum limit of $maxMediaLimit reached.');
          return false;
        }

        // Add new media to local list
        eventMedia.add(EventMedia(
            id: DateTime.now().millisecondsSinceEpoch,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            deletedAt: null,
            eventId: id,
            url: addMediaUrl,
            title: "event_media$id",
            description: "",
            type: 'image',
            category: ""));
      }

      // For addition: Send only newly uploaded media
      final mediaForApi = eventMedia
          .where((media) => media.url != null && media.url!.isNotEmpty)
          .map((media) => EventMediaPayload.fromEventMedia(media, id).toJson())
          .toList();
      logger.i('Sending only newly uploaded media to API for event $id');

      final res = await apiProvider.request(
          url: ApiUrls.UPLOADEVENTFILE,
          method: Method.POST,
          params: mediaForApi);

      if (res.data["status"] ?? false) {
        eventMedia.refresh();
        logger.i(
            'Media updated successfully. Total local media: ${eventMedia.length}');
        return true;
      } else {
        final errorMsg = res.data["message"] ?? 'Failed to update media';
        logger.e(errorMsg);
        return false;
      }
    } catch (e) {
      logger.e('Exception in updateEventMedia: $e');
      return false;
    }
  }

  Future<void> editEvent({
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      isEditing(true);
      // print(json.encode(data).toString());
      // isEditing(false);
      var res = await apiProvider.request(
          url: ApiUrls.EDITEVENT, method: Method.PUT, params: data);
      if (res.data["status"] ?? false) {
        isEditing(false);
        showSnackbar(context: context, label: 'Success', color: primaryColor);

        // Navigate back to previous screen or home after successful edit
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        } else {
          Get.offAllNamed('/lobby');
        }
      } else {
        isEditing(false);
        showSnackbar(context: context, label: 'error: ${res.data["message"]}');
      }
    } catch (e) {
      isEditing(false);
      // showSnackbar(
      //     context: context, label: 'error: $e', color: Colors.deepOrange);
      logger.e(e);
    }
  }
}

// Note: Consider refactoring DateTime conversions to a centralized utility
// to ensure .toLocal() and .toUtc() are applied consistently across the app.
