// Unified Transaction System Exports
// Single import file for all transaction-related components

// Core Components
export 'views/screens/transaction_page.dart';
export 'models/transaction_type.dart';
export 'controllers/transaction_controller.dart';
export 'services/transaction_service.dart';

// Widgets  
export 'views/widgets/transaction_item.dart';

// Utilities
export 'utils/transaction_navigation.dart';
 

/// Usage:
/// 
/// ```dart
/// import 'package:onekitty/screens/dashboard/pages/transactions/transactions_exports.dart';
/// 
/// // Now you can use any transaction component:
/// Get.toUserTransactions();
/// Get.toKittyTransactions(kittyId: 123);
/// 
/// // Or create custom configurations:
/// TransactionNavigation.toTransactionsWithConfig(
///   TransactionPageConfig(
///     transactionType: TransactionType.kitty,
///     entityId: 123,
///   ),
/// );
/// ```
