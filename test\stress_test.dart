import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:onekitty/services/permission_service.dart';
import 'package:onekitty/services/media_upload_service.dart';

import 'package:onekitty/controllers/events/event_statistics_controller.dart';
import 'package:onekitty/models/events/event_statistics_model.dart';

void main() {
  group('Stress Tests', () {
    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    group('Media Upload Service Stress Tests', () {
      late MediaUploadService uploadService;

      setUp(() {
        uploadService = MediaUploadService();
        Get.put(uploadService);
      });

      test('should handle large number of upload items', () async {
        const int itemCount = 1000;
        final stopwatch = Stopwatch()..start();

        // Add many items to queue
        for (int i = 0; i < itemCount; i++) {
          final item = MediaUploadItem.fromUrl(
            'https://example.com/image_$i.jpg',
            MediaType.image,
          );
          uploadService.addToUploadQueue(item);
        }

        stopwatch.stop();
        print('Added $itemCount items in ${stopwatch.elapsedMilliseconds}ms');

        expect(uploadService.uploadQueue.length, equals(itemCount));
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in under 5 seconds
      });

      test('should handle rapid queue modifications', () async {
        const int operations = 500;
        final random = Random();
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < operations; i++) {
          if (random.nextBool() && uploadService.uploadQueue.isNotEmpty) {
            // Remove random item
            final randomIndex = random.nextInt(uploadService.uploadQueue.length);
            final item = uploadService.uploadQueue[randomIndex];
            uploadService.removeFromUploadQueue(item.id);
          } else {
            // Add new item
            final item = MediaUploadItem.fromUrl(
              'https://example.com/stress_test_$i.jpg',
              MediaType.image,
            );
            uploadService.addToUploadQueue(item);
          }
        }

        stopwatch.stop();
        print('Performed $operations queue operations in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
      });

      test('should handle concurrent access to upload queue', () async {
        const int concurrentOperations = 100;
        final futures = <Future>[];

        for (int i = 0; i < concurrentOperations; i++) {
          futures.add(Future(() {
            final item = MediaUploadItem.fromUrl(
              'https://example.com/concurrent_$i.jpg',
              MediaType.image,
            );
            uploadService.addToUploadQueue(item);
          }));
        }

        await Future.wait(futures);
        expect(uploadService.uploadQueue.length, equals(concurrentOperations));
      });
    });

    group('Event Statistics Controller Stress Tests', () {
      late EventStatisticsController statsController;

      setUp(() {
        statsController = EventStatisticsController();
        Get.put(statsController);
      });

      test('should handle large datasets efficiently', () {
        const int dataPoints = 10000;
        final stopwatch = Stopwatch()..start();

        // Generate large dataset
        final ticketSales = <Map<String, dynamic>>[];
        for (int i = 0; i < dataPoints; i++) {
          ticketSales.add({
            'ticket_type': 'Type_$i',
            'tickets_sold': Random().nextInt(1000),
            'total_tickets': 1000 + Random().nextInt(500),
            'revenue': Random().nextDouble() * 50000,
          });
        }

        // Create mock overview data for testing
        final mockOverview = Overview(
          averageTicketPrice: 25.0,
          totalAttendees: dataPoints,
          totalEvents: 5,
          totalRevenue: dataPoints * 25.0,
          totalTicketsSold: dataPoints,
        );
        statsController.overview.value = mockOverview;

        // Perform calculations
        final metrics = statsController.getPerformanceMetrics();

        stopwatch.stop();
        print('Processed $dataPoints data points in ${stopwatch.elapsedMilliseconds}ms');

        expect(metrics.isNotEmpty, isTrue);
        expect(metrics['total_tickets_sold'], equals(dataPoints));
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      });

      test('should handle rapid data updates', () async {
        const int updates = 1000;
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < updates; i++) {
          // Create new overview data for each update
          final mockOverview = Overview(
            averageTicketPrice: 25.0,
            totalAttendees: i,
            totalEvents: 5,
            totalRevenue: i * 25.0,
            totalTicketsSold: i,
          );
          statsController.overview.value = mockOverview;

          // Trigger reactive updates
          final metrics = statsController.getPerformanceMetrics();
          expect(metrics.isNotEmpty, isTrue);
        }

        stopwatch.stop();
        print('Performed $updates data updates in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Permission Service Stress Tests', () {
      late PermissionService permissionService;

      setUp(() {
        permissionService = PermissionService();
        Get.put(permissionService);
      });

      test('should handle rapid permission state changes', () {
        const int stateChanges = 1000;
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < stateChanges; i++) {
          // Simulate permission state changes
          permissionService.resetPermissions();
          
          // Access permission states
          final contactsGranted = permissionService.contactsPermissionGranted;
          final cameraGranted = permissionService.cameraPermissionGranted;
          final storageGranted = permissionService.storagePermissionGranted;
          
          expect(contactsGranted, isFalse);
          expect(cameraGranted, isFalse);
          expect(storageGranted, isFalse);
        }

        stopwatch.stop();
        print('Performed $stateChanges permission state changes in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });
    });

    group('Memory and Performance Tests', () {
      test('should not leak memory with repeated service initialization', () {
        const int iterations = 100;
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < iterations; i++) {
          // Create and dispose services
          final uploadService = MediaUploadService();
          final statsController = EventStatisticsController();
          final permissionService = PermissionService();

          Get.put(uploadService, tag: 'test_$i');
          Get.put(statsController, tag: 'test_$i');
          Get.put(permissionService, tag: 'test_$i');

          // Use services briefly
          uploadService.clearUploadQueue();
          statsController.clearData();
          permissionService.resetPermissions();

          // Clean up
          Get.delete<MediaUploadService>(tag: 'test_$i');
          Get.delete<EventStatisticsController>(tag: 'test_$i');
          Get.delete<PermissionService>(tag: 'test_$i');
        }

        stopwatch.stop();
        print('Created and disposed $iterations service instances in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      testWidgets('should handle UI stress with rapid widget updates', (tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: StressTestWidget(),
          ),
        );

        const int updates = 100;
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < updates; i++) {
          await tester.pump(const Duration(milliseconds: 1));
        }

        stopwatch.stop();
        print('Performed $updates UI updates in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      });
    });

    group('Concurrent Operations Tests', () {
      test('should handle multiple services operating concurrently', () async {
        final uploadService = MediaUploadService();
        final statsController = EventStatisticsController();
        final permissionService = PermissionService();

        Get.put(uploadService);
        Get.put(statsController);
        Get.put(permissionService);

        const int concurrentOps = 50;
        final futures = <Future>[];

        // Upload service operations
        for (int i = 0; i < concurrentOps; i++) {
          futures.add(Future(() {
            final item = MediaUploadItem.fromUrl(
              'https://example.com/concurrent_upload_$i.jpg',
              MediaType.image,
            );
            uploadService.addToUploadQueue(item);
          }));
        }

        // Statistics operations
        for (int i = 0; i < concurrentOps; i++) {
          futures.add(Future(() {
            // Create mock overview for concurrent testing
            final mockOverview = Overview(
              averageTicketPrice: 25.0,
              totalAttendees: i,
              totalEvents: 5,
              totalRevenue: i * 25.0,
              totalTicketsSold: i,
            );
            statsController.overview.value = mockOverview;
            statsController.getPerformanceMetrics();
          }));
        }

        // Permission operations
        for (int i = 0; i < concurrentOps; i++) {
          futures.add(Future(() {
            permissionService.resetPermissions();
          }));
        }

        final stopwatch = Stopwatch()..start();
        await Future.wait(futures);
        stopwatch.stop();

        print('Completed ${futures.length} concurrent operations in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
        expect(uploadService.uploadQueue.length, equals(concurrentOps));
      });
    });

    group('Error Handling Stress Tests', () {
      test('should handle repeated error scenarios gracefully', () {
        final uploadService = MediaUploadService();
        Get.put(uploadService);

        const int errorScenarios = 1000;
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < errorScenarios; i++) {
          try {
            // Simulate error conditions
            uploadService.removeFromUploadQueue('non_existent_id_$i');
            
            // Add invalid items
            final item = MediaUploadItem.fromUrl('', MediaType.image);
            uploadService.addToUploadQueue(item);
            
          } catch (e) {
            // Expected errors should be handled gracefully
          }
        }

        stopwatch.stop();
        print('Handled $errorScenarios error scenarios in ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      });
    });
  });
}

/// Widget for UI stress testing
class StressTestWidget extends StatefulWidget {
  const StressTestWidget({super.key});

  @override
  _StressTestWidgetState createState() => _StressTestWidgetState();
}

class _StressTestWidgetState extends State<StressTestWidget> {
  int counter = 0;
  Timer? timer;

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(const Duration(milliseconds: 10), (timer) {
      if (mounted) {
        setState(() {
          counter++;
        });
      }
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Counter: $counter'),
            CircularProgressIndicator(value: (counter % 100) / 100),
            ...List.generate(10, (index) => 
              Container(
                width: 50,
                height: 20,
                color: Colors.primaries[index % Colors.primaries.length],
                margin: const EdgeInsets.all(2),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
